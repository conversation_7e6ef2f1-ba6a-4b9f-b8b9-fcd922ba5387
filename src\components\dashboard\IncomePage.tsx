import React, { useState } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Select from '../common/Select';
import Input from '../common/Input';
import { RotateCcw, Save, ArrowRight } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';

const IncomePage: React.FC = () => {
  // State for each scenario section
  const [loanModel, setLoanModel] = useState({
    enabled: false,
    fixedAmount: '',
    percentOfCash: '',
    annualLoan: '',
    annualLoanStartYear: '',
    loanDurationType: '',
    loanDurationValue: '',
    loanDistribution: '',
    customIncomeSchedule: '',
    repaymentStrategy: '',
    customRepaymentSchedule: '',
    oneTimeLoanYear: '',
    interestRates: {
      current: '',
      min: '',
      stress: '',
      fixed: '',
      variableMin: '',
      variableMax: '',
    },
    compounding: 'simple',
  });
  const [withdrawalModel, setWithdrawalModel] = useState({
    enabled: false,
    fixedAmount: '',
    percentOfCash: '',
    annualAmount: '',
    annualStartYear: '',
    durationType: '',
    durationValue: '',
    source: '',
    effectOnDeathBenefit: '',
    withdrawalTax: '',
    customSchedule: '',
    customScheduleType: '',
    interestRates: {
      current: '',
      min: '',
      stress: '',
      fixed: '',
      variableMin: '',
      variableMax: '',
    },
    compounding: 'simple',
  });
  const [increasingIncome, setIncreasingIncome] = useState({
    enabled: false,
    startAge: '',
    endAge: '',
    duration: '',
    initialAmount: '',
    solveForMax: false,
    fixedIncrease: '',
    cpiIncrease: '',
    source: '',
    effectOnDeathBenefit: '',
    withdrawalTax: '',
    interestRates: {
      current: '',
      min: '',
      stress: '',
      fixed: '',
      variableMin: '',
      variableMax: '',
    },
    compounding: 'simple',
  });
  const [levelIncome, setLevelIncome] = useState({
    enabled: false,
    startAge: '',
    endAge: '',
    duration: '',
    trigger: '',
    annualAmount: '',
    source: '',
    effectOnDeathBenefit: '',
    withdrawalTax: '',
    interestRates: {
      current: '',
      min: '',
      stress: '',
      fixed: '',
      variableMin: '',
      variableMax: '',
    },
    compounding: 'simple',
  });
  const [premiumFinance, setPremiumFinance] = useState({
    enabled: false,
    type: '',
    annualLoan: '',
    duration: '',
    startYear: '',
    totalFinanced: '',
    repayment: '',
    interestRates: {
      current: '',
      min: '',
      stress: '',
      fixed: '',
      variableMin: '',
      variableMax: '',
    },
    compounding: 'simple',
  });

  const { selectedCustomerData, selectedPolicyData, setActiveTab } = useDashboard();

  // Placeholder for schedule button
  const handleSchedule = (section: string) => {
    alert(`Open schedule editor for: ${section}`);
  };

  // Save all scenarios (placeholder)
  const handleSaveAll = () => {
    alert('All selected income scenarios saved!');
  };

  // Reset all scenario state
  const handleResetScenarios = () => {
    setLoanModel({
      enabled: false,
      fixedAmount: '',
      percentOfCash: '',
      annualLoan: '',
      annualLoanStartYear: '',
      loanDurationType: '',
      loanDurationValue: '',
      loanDistribution: '',
      customIncomeSchedule: '',
      repaymentStrategy: '',
      customRepaymentSchedule: '',
      oneTimeLoanYear: '',
      interestRates: {
        current: '',
        min: '',
        stress: '',
        fixed: '',
        variableMin: '',
        variableMax: '',
      },
      compounding: 'simple',
    });
    setWithdrawalModel({
      enabled: false,
      fixedAmount: '',
      percentOfCash: '',
      annualAmount: '',
      annualStartYear: '',
      durationType: '',
      durationValue: '',
      source: '',
      effectOnDeathBenefit: '',
      withdrawalTax: '',
      customSchedule: '',
      customScheduleType: '',
      interestRates: {
        current: '',
        min: '',
        stress: '',
        fixed: '',
        variableMin: '',
        variableMax: '',
      },
      compounding: 'simple',
    });
    setIncreasingIncome({
      enabled: false,
      startAge: '',
      endAge: '',
      duration: '',
      initialAmount: '',
      solveForMax: false,
      fixedIncrease: '',
      cpiIncrease: '',
      source: '',
      effectOnDeathBenefit: '',
      withdrawalTax: '',
      interestRates: {
        current: '',
        min: '',
        stress: '',
        fixed: '',
        variableMin: '',
        variableMax: '',
      },
      compounding: 'simple',
    });
    setLevelIncome({
      enabled: false,
      startAge: '',
      endAge: '',
      duration: '',
      trigger: '',
      annualAmount: '',
      source: '',
      effectOnDeathBenefit: '',
      withdrawalTax: '',
      interestRates: {
        current: '',
        min: '',
        stress: '',
        fixed: '',
        variableMin: '',
        variableMax: '',
      },
      compounding: 'simple',
    });
    setPremiumFinance({
      enabled: false,
      type: '',
      annualLoan: '',
      duration: '',
      startYear: '',
      totalFinanced: '',
      repayment: '',
      interestRates: {
        current: '',
        min: '',
        stress: '',
        fixed: '',
        variableMin: '',
        variableMax: '',
      },
      compounding: 'simple',
    });
    alert('All income scenarios have been reset!');
  };

  // Helper for interest/compounding box
  const InterestBox = ({ state, setState }: { state: any, setState: any }) => (
    <div className="p-4 border rounded-lg bg-gray-50 mt-4">
      <div className="font-semibold mb-2 text-black">Run projections under the following interest rate assumptions:</div>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-2">
        <Input label="Current crediting rate" value={state.interestRates.current} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setState((prev: any) => ({ ...prev, interestRates: { ...prev.interestRates, current: e.target.value } }))} placeholder="%" className="text-black placeholder-black" />
        <Input label="Guaranteed minimum rate" value={state.interestRates.min} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setState((prev: any) => ({ ...prev, interestRates: { ...prev.interestRates, min: e.target.value } }))} placeholder="%" className="text-black placeholder-black" />
        <Input label="Stress scenario rate" value={state.interestRates.stress} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setState((prev: any) => ({ ...prev, interestRates: { ...prev.interestRates, stress: e.target.value } }))} placeholder="%" className="text-black placeholder-black" />
      </div>
      <div className="font-semibold mb-2 text-black">Loan interest rate assumptions:</div>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-2">
        <Input label="Fixed Interest rate" value={state.interestRates.fixed} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setState((prev: any) => ({ ...prev, interestRates: { ...prev.interestRates, fixed: e.target.value } }))} placeholder="%" className="text-black placeholder-black" />
        <Input label="Variable loan rate min" value={state.interestRates.variableMin} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setState((prev: any) => ({ ...prev, interestRates: { ...prev.interestRates, variableMin: e.target.value } }))} placeholder="%" className="text-black placeholder-black" />
        <Input label="Variable loan rate max" value={state.interestRates.variableMax} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setState((prev: any) => ({ ...prev, interestRates: { ...prev.interestRates, variableMax: e.target.value } }))} placeholder="%" className="text-black placeholder-black" />
      </div>
      <div className="font-semibold mb-2 text-black">Compounding method:</div>
      <Select label="Compounding" value={state.compounding} onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setState((prev: any) => ({ ...prev, compounding: e.target.value }))} options={[{ value: 'simple', label: 'Simple' }, { value: 'compounded', label: 'Compounded annually' }]} className="text-black" />
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Title and Stepper/Flow Indicator */}

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Income illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <div className="min-h-screen bg-gray-50 p-4 space-y-8">
          {/* 1. Loan Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">1. Model Loan Amount to Illustrate</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={loanModel.enabled} onChange={e => setLoanModel(prev => ({ ...prev, enabled: e.target.checked }))} />
                <span className="font-semibold text-black">Yes, Model Loan amount to illustrate</span>
              </div>
              {loanModel.enabled && (
                <div className="space-y-4 pl-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input label="Fixed amount ($)" value={loanModel.fixedAmount} onChange={e => setLoanModel(prev => ({ ...prev, fixedAmount: e.target.value }))} className="text-black placeholder-black" />
                    <Input label="% of available cash value" value={loanModel.percentOfCash} onChange={e => setLoanModel(prev => ({ ...prev, percentOfCash: e.target.value }))} className="text-black placeholder-black" />
                    <div className="flex items-end space-x-2">
                      <Input label="Annual loan ($/year)" value={loanModel.annualLoan} onChange={e => setLoanModel(prev => ({ ...prev, annualLoan: e.target.value }))} className="text-black placeholder-black" />
                      <Input label="Starting Policy Year" value={loanModel.annualLoanStartYear} onChange={e => setLoanModel(prev => ({ ...prev, annualLoanStartYear: e.target.value }))} className="text-black placeholder-black" />
                    </div>
                  </div>
                  <div className="font-semibold mt-4">Loan duration</div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Input label="For ___ years" value={loanModel.loanDurationType === 'years' ? loanModel.loanDurationValue : ''} onChange={e => setLoanModel(prev => ({ ...prev, loanDurationType: 'years', loanDurationValue: e.target.value }))} className="text-black placeholder-black" />
                    <Input label="Until age" value={loanModel.loanDurationType === 'age' ? loanModel.loanDurationValue : ''} onChange={e => setLoanModel(prev => ({ ...prev, loanDurationType: 'age', loanDurationValue: e.target.value }))} className="text-black placeholder-black" />
                    <div className="flex items-center space-x-2">
                      <input type="radio" checked={loanModel.loanDurationType === 'to100'} onChange={() => setLoanModel(prev => ({ ...prev, loanDurationType: 'to100', loanDurationValue: '' }))} />
                      <span className="text-black">To age 100 / to life expectancy</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="radio" checked={loanModel.loanDurationType === 'ongoing'} onChange={() => setLoanModel(prev => ({ ...prev, loanDurationType: 'ongoing', loanDurationValue: '' }))} />
                      <span className="text-black">Throughout policy duration/maturity (ongoing loan)</span>
                    </div>
                  </div>
                  <div className="font-semibold mt-4">Loan distribution structure</div>
                  <Select label="Distribution" value={loanModel.loanDistribution} onChange={e => setLoanModel(prev => ({ ...prev, loanDistribution: e.target.value }))} options={[
                    //{ value: '', label: 'Select...' },
                    { value: 'level', label: 'Level income stream (same amount annually)' },
                    { value: 'increasing', label: 'Increasing income stream' },
                    { value: 'custom', label: 'Custom income stream' },
                  ]} className="text-black" />
                  {loanModel.loanDistribution === 'custom' && (
                    <div className="flex items-center mt-2">
                      <Button onClick={() => handleSchedule('Loan Custom Income Stream')} className="ml-2">Schedule</Button>
                    </div>
                  )}
                  {/* Repayment strategy */}
                  <div className="font-semibold mt-4">Repayment strategy</div>
                  <Select label="Repayment" value={loanModel.repaymentStrategy} onChange={e => setLoanModel(prev => ({ ...prev, repaymentStrategy: e.target.value }))} options={[
                    //{ value: '', label: 'Select...' },
                    { value: 'none', label: 'No repayment (loan balance reduces death benefit)' },
                    { value: 'lump', label: 'Lump-sum repayment at death or maturity' },
                    { value: 'periodic', label: 'Periodic/Custom Repayment' },
                  ]} className="text-black" />
                  {loanModel.repaymentStrategy && (
                    <div className="flex items-center mt-2">
                      <Button onClick={() => handleSchedule('Loan Custom Repayment')} className="ml-2">Schedule</Button>
                    </div>
                  )}
                  <Input label="One-time loan in Policy Year" value={loanModel.oneTimeLoanYear} onChange={e => setLoanModel(prev => ({ ...prev, oneTimeLoanYear: e.target.value }))} className="text-black placeholder-black" />
                  <InterestBox state={loanModel} setState={setLoanModel} />
                </div>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <input type="checkbox" checked={!loanModel.enabled} onChange={e => setLoanModel(prev => ({ ...prev, enabled: !e.target.checked }))} />
                <span className="font-semibold text-black">No, do not model loan projections.</span>
              </div>
            </div>
          </Card>

          {/* 2. Withdrawal Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">2. Model Policy Withdrawals/Income</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={withdrawalModel.enabled} onChange={e => setWithdrawalModel(prev => ({ ...prev, enabled: e.target.checked }))} />
                <span className="font-semibold text-black">Yes, Model different types of withdrawal to illustrate</span>
              </div>
              {withdrawalModel.enabled && (
                <div className="space-y-4 pl-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input label="Fixed amount ($)" value={withdrawalModel.fixedAmount} onChange={e => setWithdrawalModel(prev => ({ ...prev, fixedAmount: e.target.value }))} className="text-black placeholder-black" />
                    <Input label="% of available cash value" value={withdrawalModel.percentOfCash} onChange={e => setWithdrawalModel(prev => ({ ...prev, percentOfCash: e.target.value }))} className="text-black placeholder-black" />
                    <div className="flex items-end space-x-2">
                      <Input label="Annual Amount ($/year)" value={withdrawalModel.annualAmount} onChange={e => setWithdrawalModel(prev => ({ ...prev, annualAmount: e.target.value }))} className="text-black placeholder-black" />
                      <Input label="Starting Policy Year" value={withdrawalModel.annualStartYear} onChange={e => setWithdrawalModel(prev => ({ ...prev, annualStartYear: e.target.value }))} className="text-black placeholder-black" />
                    </div>
                  </div>
                  <div className="font-semibold mt-4">Duration</div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Input label="For ___ years" value={withdrawalModel.durationType === 'years' ? withdrawalModel.durationValue : ''} onChange={e => setWithdrawalModel(prev => ({ ...prev, durationType: 'years', durationValue: e.target.value }))} className="text-black placeholder-black" />
                    <Input label="Until age" value={withdrawalModel.durationType === 'age' ? withdrawalModel.durationValue : ''} onChange={e => setWithdrawalModel(prev => ({ ...prev, durationType: 'age', durationValue: e.target.value }))} className="text-black placeholder-black" />
                    <div className="flex items-center space-x-2">
                      <input type="radio" checked={withdrawalModel.durationType === 'to100'} onChange={() => setWithdrawalModel(prev => ({ ...prev, durationType: 'to100', durationValue: '' }))} />
                      <span className="text-black">To age 100 / to life expectancy</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input type="radio" checked={withdrawalModel.durationType === 'ongoing'} onChange={() => setWithdrawalModel(prev => ({ ...prev, durationType: 'ongoing', durationValue: '' }))} />
                      <span className="text-black">Throughout policy duration/maturity (ongoing)</span>
                    </div>
                  </div>
                  <div className="font-semibold mt-4">Source of withdrawal</div>
                  <Select label="Source" value={withdrawalModel.source} onChange={e => setWithdrawalModel(prev => ({ ...prev, source: e.target.value }))} options={[
                    { value: '', label: 'Select' },
                    { value: 'cash', label: 'From available cash value' },
                    { value: 'surrender', label: 'From surrender value only' },
                    { value: 'partial', label: 'Partial surrender (reduce face amount)' },
                  ]} className="text-black" />
                  <div className="font-semibold mt-4">Effect on death benefit</div>
                  <Select label="Effect" value={withdrawalModel.effectOnDeathBenefit} onChange={e => setWithdrawalModel(prev => ({ ...prev, effectOnDeathBenefit: e.target.value }))} options={[
                    { value: '', label: 'Select' },
                    { value: 'reduce', label: 'Reduce dollar-for-dollar' },
                    { value: 'option1', label: 'Switch to Option 1 (Level) after withdrawal' },
                    { value: 'option2', label: 'Maintain Option 2 (Increasing)' },
                    { value: 'compare', label: 'Compare both effects' },
                  ]} className="text-black" />
                  <div className="font-semibold mt-4">Withdrawal tax note (if applicable)</div>
                  <Select label="Tax Note" value={withdrawalModel.withdrawalTax} onChange={e => setWithdrawalModel(prev => ({ ...prev, withdrawalTax: e.target.value }))} options={[
                    { value: '', label: 'Select' },
                    { value: 'taxfree', label: 'Model tax-free withdrawal (basis first)' },
                    { value: 'taximpact', label: 'Include tax impact if exceeding cost basis' },
                  ]} className="text-black" />
                  <div className="font-semibold mt-4">Custom withdrawal schedule</div>
                  <Select label="Custom Schedule Type" value={withdrawalModel.customScheduleType} onChange={e => setWithdrawalModel(prev => ({ ...prev, customScheduleType: e.target.value }))} options={[
                    { value: '', label: 'Select' },
                    { value: 'yearly', label: 'Use specific yearly withdrawals' },
                    { value: 'peryear', label: 'Amount per year from age __ to __' },
                    { value: 'step', label: 'Amount for first __ years, then reduce/increase' },
                    { value: 'schedule', label: 'Schedule as' },
                  ]} className="text-black" />
                  {withdrawalModel.customScheduleType === 'schedule' && (
                    <div className="flex items-center mt-2">
                      <Button onClick={() => handleSchedule('Withdrawal Custom Schedule')} className="ml-2">Schedule</Button>
                    </div>
                  )}
                  <InterestBox state={withdrawalModel} setState={setWithdrawalModel} />
                </div>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <input type="checkbox" checked={!withdrawalModel.enabled} onChange={e => setWithdrawalModel(prev => ({ ...prev, enabled: !e.target.checked }))} />
                <span className="font-semibold text-black">No, do not model policy withdrawals.</span>
              </div>
            </div>
          </Card>

          {/* 3. Increasing/Level Income Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">3. Model Increasing/Level Income Stream</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={increasingIncome.enabled} onChange={e => setIncreasingIncome(prev => ({ ...prev, enabled: e.target.checked }))} />
                <span className="font-semibold text-black">Yes, Model Increasing Income</span>
              </div>
              {increasingIncome.enabled && (
                <div className="space-y-4 pl-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input label="Start age" value={increasingIncome.startAge} onChange={e => setIncreasingIncome(prev => ({ ...prev, startAge: e.target.value }))} className="text-black placeholder-black" />
                    <Input label="End age" value={increasingIncome.endAge} onChange={e => setIncreasingIncome(prev => ({ ...prev, endAge: e.target.value }))} className="text-black placeholder-black" />
                    <Input label="Duration (years)" value={increasingIncome.duration} onChange={e => setIncreasingIncome(prev => ({ ...prev, duration: e.target.value }))} className="text-black placeholder-black" />
                  </div>
                  <Input label="Initial annual income amount ($/year)" value={increasingIncome.initialAmount} onChange={e => setIncreasingIncome(prev => ({ ...prev, initialAmount: e.target.value }))} className="text-black placeholder-black" />
                  <div className="flex items-center space-x-2">
                    <input type="checkbox" checked={increasingIncome.solveForMax} onChange={e => setIncreasingIncome(prev => ({ ...prev, solveForMax: e.target.checked }))} />
                    <span className="text-black">Solve for max sustainable starting income with growth rate</span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input label="Fixed % increase per year" value={increasingIncome.fixedIncrease} onChange={e => setIncreasingIncome(prev => ({ ...prev, fixedIncrease: e.target.value }))} className="text-black placeholder-black" />
                    <Input label="CPI or inflation-indexed %" value={increasingIncome.cpiIncrease} onChange={e => setIncreasingIncome(prev => ({ ...prev, cpiIncrease: e.target.value }))} className="text-black placeholder-black" />
                  </div>
                  <div className="font-semibold mt-4">Source of withdrawal</div>
                  <Select label="Source" value={increasingIncome.source} onChange={e => setIncreasingIncome(prev => ({ ...prev, source: e.target.value }))} options={[
                    { value: '', label: 'Select' },
                    { value: 'cash', label: 'From available cash value' },
                    { value: 'surrender', label: 'From surrender value only' },
                    { value: 'partial', label: 'Partial surrender (reduce face amount)' },
                  ]} className="text-black" />
                  <div className="font-semibold mt-4">Effect on death benefit</div>
                  <Select label="Effect" value={increasingIncome.effectOnDeathBenefit} onChange={e => setIncreasingIncome(prev => ({ ...prev, effectOnDeathBenefit: e.target.value }))} options={[
                    { value: '', label: 'Select' },
                    { value: 'reduce', label: 'Reduce dollar-for-dollar' },
                    { value: 'option1', label: 'Switch to Option 1 (Level) after withdrawal' },
                    { value: 'option2', label: 'Maintain Option 2 (Increasing)' },
                    { value: 'compare', label: 'Compare both effects' },
                  ]} className="text-black" />
                  <div className="font-semibold mt-4">Withdrawal tax note (if applicable)</div>
                  <Select label="Tax Note" value={increasingIncome.withdrawalTax} onChange={e => setIncreasingIncome(prev => ({ ...prev, withdrawalTax: e.target.value }))} options={[
                    { value: '', label: 'Select' },
                    { value: 'taxfree', label: 'Model tax-free withdrawal (basis first)' },
                    { value: 'taximpact', label: 'Include tax impact if exceeding cost basis' },
                  ]} className="text-black" />
                  <InterestBox state={increasingIncome} setState={setIncreasingIncome} />
                </div>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <input type="checkbox" checked={!increasingIncome.enabled} onChange={e => setIncreasingIncome(prev => ({ ...prev, enabled: !e.target.checked }))} />
                <span className="font-semibold text-black">No, do not model income stream.</span>
              </div>
            </div>
          </Card>

          {/* 4. Level Income Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">4. Model Level Income Stream</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={levelIncome.enabled} onChange={e => setLevelIncome(prev => ({ ...prev, enabled: e.target.checked }))} />
                <span className="font-semibold text-black">Yes, Model Level Income</span>
              </div>
              {levelIncome.enabled && (
                <div className="space-y-4 pl-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input label="Start age" value={levelIncome.startAge} onChange={e => setLevelIncome(prev => ({ ...prev, startAge: e.target.value }))} className="text-black placeholder-black" />
                    <Input label="End age" value={levelIncome.endAge} onChange={e => setLevelIncome(prev => ({ ...prev, endAge: e.target.value }))} className="text-black placeholder-black" />
                    <Input label="Duration (years)" value={levelIncome.duration} onChange={e => setLevelIncome(prev => ({ ...prev, duration: e.target.value }))} className="text-black placeholder-black" />
                  </div>
                  <Input label="Trigger event" value={levelIncome.trigger} onChange={e => setLevelIncome(prev => ({ ...prev, trigger: e.target.value }))} className="text-black placeholder-black" />
                  <Input label="Annual income amount ($/year)" value={levelIncome.annualAmount} onChange={e => setLevelIncome(prev => ({ ...prev, annualAmount: e.target.value }))} className="text-black placeholder-black" />
                  <div className="font-semibold mt-4">Source of withdrawal</div>
                  <Select label="Source" value={levelIncome.source} onChange={e => setLevelIncome(prev => ({ ...prev, source: e.target.value }))} options={[
                    { value: '', label: 'Select' },
                    { value: 'cash', label: 'From available cash value' },
                    { value: 'surrender', label: 'From surrender value only' },
                    { value: 'partial', label: 'Partial surrender (reduce face amount)' },
                  ]} className="text-black" />
                  <div className="font-semibold mt-4">Effect on death benefit</div>
                  <Select label="Effect" value={levelIncome.effectOnDeathBenefit} onChange={e => setLevelIncome(prev => ({ ...prev, effectOnDeathBenefit: e.target.value }))} options={[
                    { value: '', label: 'Select' },
                    { value: 'reduce', label: 'Reduce dollar-for-dollar' },
                    { value: 'option1', label: 'Switch to Option 1 (Level) after withdrawal' },
                    { value: 'option2', label: 'Maintain Option 2 (Increasing)' },
                    { value: 'compare', label: 'Compare both effects' },
                  ]} className="text-black" />
                  <div className="font-semibold mt-4">Withdrawal tax note (if applicable)</div>
                  <Select label="Tax Note" value={levelIncome.withdrawalTax} onChange={e => setLevelIncome(prev => ({ ...prev, withdrawalTax: e.target.value }))} options={[
                    { value: '', label: 'Select' },
                    { value: 'taxfree', label: 'Model tax-free withdrawal (basis first)' },
                    { value: 'taximpact', label: 'Include tax impact if exceeding cost basis' },
                  ]} className="text-black" />
                  <InterestBox state={levelIncome} setState={setLevelIncome} />
                </div>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <input type="checkbox" checked={!levelIncome.enabled} onChange={e => setLevelIncome(prev => ({ ...prev, enabled: !e.target.checked }))} />
                <span className="font-semibold text-black">No, do not model level income stream.</span>
              </div>
            </div>
          </Card>

          {/* 5. Premium Finance Section */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">5. Model Premium Financing</h2>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <input type="checkbox" checked={premiumFinance.enabled} onChange={e => setPremiumFinance(prev => ({ ...prev, enabled: e.target.checked }))} />
                <span className="font-semibold text-black">Yes, model type of premium finance arrangement</span>
              </div>
              {premiumFinance.enabled && (
                <div className="space-y-4 pl-6">
                  <Select label="Finance Type" value={premiumFinance.type} onChange={e => setPremiumFinance(prev => ({ ...prev, type: e.target.value }))} options={[
                    { value: '', label: 'Select' },
                    { value: 'full', label: 'Loan pays full annual premium' },
                    { value: 'part', label: 'Loan pays part of premium (shared contribution)' },
                    { value: 'interest', label: 'Interest-only loan (repay principal later)' },
                    { value: 'principal', label: 'Principal + interest repayment model' },
                  ]} className="text-black" />
                  <Input label="Annual loan amount ($)" value={premiumFinance.annualLoan} onChange={e => setPremiumFinance(prev => ({ ...prev, annualLoan: e.target.value }))} className="text-black placeholder-black" />
                  <Input label="Loan duration (years)" value={premiumFinance.duration} onChange={e => setPremiumFinance(prev => ({ ...prev, duration: e.target.value }))} className="text-black placeholder-black" />
                  <Input label="Loan start year" value={premiumFinance.startYear} onChange={e => setPremiumFinance(prev => ({ ...prev, startYear: e.target.value }))} className="text-black placeholder-black" />
                  <Input label="Total premium financed ($)" value={premiumFinance.totalFinanced} onChange={e => setPremiumFinance(prev => ({ ...prev, totalFinanced: e.target.value }))} className="text-black placeholder-black" />
                  <Select label="Repayment strategy" value={premiumFinance.repayment} onChange={e => setPremiumFinance(prev => ({ ...prev, repayment: e.target.value }))} options={[
                    { value: '', label: 'Select' },
                    { value: 'external', label: 'Repay using external funds' },
                    { value: 'policy', label: 'Repay using policy values (cash value / death benefit)' },
                    { value: 'none', label: 'No repayment (loan reduces death benefit at maturity)' },
                  ]} className="text-black" />
                  <InterestBox state={premiumFinance} setState={setPremiumFinance} />
                </div>
              )}
              <div className="flex items-center space-x-4 mt-2">
                <input type="checkbox" checked={!premiumFinance.enabled} onChange={e => setPremiumFinance(prev => ({ ...prev, enabled: !e.target.checked }))} />
                <span className="font-semibold text-black">No, do not model premium financing.</span>
              </div>
            </div>
          </Card>
          {/* Save and Reset Buttons (refactored to match AsIsPage) */}
          <div className="flex flex-wrap gap-4 justify-center mt-8">
            <Button onClick={handleSaveAll}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none px-8 py-3 text-lg font-semibold">
              <Save className="w-4 h-4" />
              <span>Save All Selected Scenarios</span>
            </Button>
            <Button onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none px-8 py-3 text-lg font-semibold">
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
            <Button
              onClick={() => alert('Proceed to next step (e.g., Loan Repayment Illustration)')}
              className="flex items-center space-x-2">
              <span>Proceed to Loan Repayment Illustration</span>
              <ArrowRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default IncomePage;