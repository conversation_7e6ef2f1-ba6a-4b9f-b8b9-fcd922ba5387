import React, { useState } from 'react';
import { useDashboard } from '../../contexts/DashboardContext';
import AsIsPage from './AsIsPage';
import FaceAmountPage from './FaceAmountPage';
import PremiumPage from './PremiumPage';
import IncomePage from './IncomePage';
import LoanRepaymentPage from './LoanRepaymentPage';
import InterestRatePage from './InterestRatePage';
import Card from '../common/Card';
import { Calculator, ChevronDown, ChevronRight, Bookmark, DollarSign, TrendingUp, Download, Percent } from 'lucide-react';
import SelectedScenariosPage from './SelectedScenariosPage';

interface IllustrationTab {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  description: string;
}

const IllustrationMainPage: React.FC = () => {
  const { activeTab, setActiveTab, selectedCustomerData, selectedPolicyData, scenarios } = useDashboard();
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  const illustrationTabs: IllustrationTab[] = [
    {
      id: 'as-is',
      label: 'AS-IS',
      icon: Bookmark,
      description: 'Current policy baseline illustration'
    },
    {
      id: 'face-amount',
      label: 'Face Amount',
      icon: DollarSign,
      description: 'Death benefit amount scenarios'
    },
    {
      id: 'premium',
      label: 'Premium',
      icon: TrendingUp,
      description: 'Premium payment scenarios'
    },
    {
      id: 'interest-rate',
      label: 'Interest Rate',
      icon: Percent,
      description: 'Interest rate based scenarios'
    },
    {
      id: 'income',
      label: 'FULL SURRENDER / INCOME (LOAN & WITHDRAWAL)',
      icon: Download,
      description: 'Loan & withdrawal scenarios'
    },
    {
      id: 'loan-repayment',
      label: 'Loan Repayment',
      icon: Percent,
      description: 'Loan repayment scenarios'
    }
  ];

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    // Toggle the section when clicking on a tab
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(tabId)) {
      newExpanded.delete(tabId);
    } else {
      newExpanded.add(tabId);
    }
    setExpandedSections(newExpanded);
  };

  const getScenariosForCategory = (category: string) => {
    return scenarios.filter(scenario => scenario.category === category);
  };

  const renderIllustrationContent = () => {
    switch (activeTab) {
      case 'selected-scenarios':
        return <SelectedScenariosPage />;
      case 'as-is':
        return <AsIsPage />;
      case 'face-amount':
        return <FaceAmountPage />;
      case 'premium':
        return <PremiumPage />;
      case 'income':
        return <IncomePage />;
      case 'loan-repayment':
        return <LoanRepaymentPage />;
      case 'interest-rate':
        return <InterestRatePage />;
      default:
        return <AsIsPage />;
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-[#121212]">
      {/* Current Policy Information at the top */}
      {selectedCustomerData && selectedPolicyData && (
        <div className="px-4 pt-4">
          <Card>
            <div className="flex items-center space-x-3 mb-6">
              <Calculator className="w-6 h-6 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Current Policy Information</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Policy Number</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{selectedCustomerData.policyNumber || selectedCustomerData.details["Policy Number"] || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Customer Name</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{selectedCustomerData.name || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Customer ID</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{selectedCustomerData.customerId || selectedCustomerData.details["Customer ID"] || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Policy Type</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{selectedPolicyData.name || selectedCustomerData.details["Policy Type"] || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Face Amount ($)</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{selectedPolicyData.coverage || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Annual Premium ($)</p>
                <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">{selectedPolicyData.premium || 'N/A'}</p>
              </div>
            </div>
          </Card>
        </div>
      )}
      {/* Vertical Dropdown Navigation - Full Width */}
      <div className="flex-1 overflow-auto">
        <div className="w-full px-4 py-6">
          {/* Show message if no policy is selected */}
          {(!selectedCustomerData || !selectedPolicyData) && (
            <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 mb-8">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
                  <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
                  <p className="text-yellow-700 dark:text-yellow-300">
                    Please go to the Policy Selection tab first to search and select a customer policy before configuring illustrations.
                  </p>
                </div>
              </div>
            </Card>
          )}

          <div className="space-y-4">
            {illustrationTabs.map((tab) => {
              const Icon = tab.icon;
              const isExpanded = expandedSections.has(tab.id);
              const isActive = activeTab === tab.id;
              const tabScenarios = getScenariosForCategory(tab.id);

              return (
                <div key={tab.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                  {/* Dropdown Header */}
                  <button
                    onClick={() => handleTabClick(tab.id)}
                    className={`w-full flex items-center justify-between p-6 text-left transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded-lg ${
                      isExpanded ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-lg ${
                        isActive
                          ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                      }`}>
                        <Icon className="w-6 h-6" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{tab.label}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{tab.description}</p>
                        {tabScenarios.length > 0 && (
                          <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                            {tabScenarios.length} scenario{tabScenarios.length !== 1 ? 's' : ''} available
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {isExpanded ? (
                        <ChevronDown className="w-5 h-5 text-gray-400 transition-transform duration-200" />
                      ) : (
                        <ChevronRight className="w-5 h-5 text-gray-400 transition-transform duration-200" />
                      )}
                    </div>
                  </button>

                  {/* Dropdown Content */}
                  {isExpanded && (
                    <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
                      <div className="p-6">
                        {/* Scenarios List */}
                        {tabScenarios.length > 0 && (
                          <div className="mb-6">
                            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Available Scenarios</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                              {tabScenarios.map((scenario) => (
                                <div
                                  key={scenario.id}
                                  className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200 cursor-pointer"
                                >
                                  <h5 className="font-medium text-gray-900 dark:text-gray-100 text-sm">{scenario.name}</h5>
                                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{scenario.asIsDetails}</p>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Show Content Directly Under Tab When Active */}
                        {isActive && (
                          <div>
                            {renderIllustrationContent()}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* No separate content area - content is shown directly under each tab */}
        </div>
      </div>
    </div>
  );
};

export default IllustrationMainPage;
